# coding=utf-8
"""
瀚海知识库管理示例
演示如何使用瀚海客户端进行知识库和文档管理
"""

import os
import time
from teamvision.teamvision.ai.hanhai_client import (
    create_client, 
    HanHaiClientError,
    AnalysisStrategy
)


def example_dataset_management():
    """知识库管理示例"""
    print("=== 知识库管理示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-dataset-key"  # 注意：知识库API使用DATASET_KEY
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 1. 创建知识库
            print("1. 创建知识库...")
            dataset = client.create_dataset("AI测试知识库")
            print(f"✓ 创建成功: {dataset.name} (ID: {dataset.id})")
            
            # 2. 获取知识库列表
            print("\n2. 获取知识库列表...")
            datasets = client.get_datasets(page=1, limit=10)
            print(f"✓ 共有 {datasets['total']} 个知识库")
            
            for i, ds in enumerate(datasets['data'][:3], 1):
                print(f"  {i}. {ds.name} - 文档数: {ds.document_count}")
            
            # 3. 删除知识库（可选）
            # print(f"\n3. 删除知识库 {dataset.id}...")
            # success = client.delete_dataset(dataset.id)
            # if success:
            #     print("✓ 删除成功")
            # else:
            #     print("✗ 删除失败")
            
        except HanHaiClientError as e:
            print(f"✗ 知识库管理失败: {e}")


def example_document_management():
    """文档管理示例"""
    print("\n=== 文档管理示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-dataset-key"
    DATASET_ID = "your-dataset-id"  # 请替换为实际的知识库ID
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 1. 上传文档到知识库
            print("1. 上传文档到知识库...")
            
            # 创建示例文档文件
            sample_file = "sample_document.txt"
            with open(sample_file, 'w', encoding='utf-8') as f:
                f.write("""
这是一个示例文档。

# 人工智能简介

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

## 主要应用领域

1. 自然语言处理
2. 计算机视觉
3. 机器学习
4. 深度学习
5. 专家系统

## 发展趋势

随着技术的不断进步，人工智能在各个领域都有着广泛的应用前景。
                """)
            
            try:
                # 配置文档解析策略（可选）
                analysis_strategy = AnalysisStrategy(
                    image_understanding={
                        "enable": True,
                        "method": "vlm",
                        "vlm": {
                            "model_name": "gpt-4o",
                            "provider_name": "qzhou",
                            "prompt": {
                                "role": "user",
                                "text": "请描述这张图片的内容"
                            }
                        }
                    },
                    table_understanding={
                        "enable": True,
                        "method": "vlm",
                        "vlm": {
                            "model_name": "gpt-4o",
                            "provider_name": "qzhou",
                            "prompt": {
                                "role": "user",
                                "text": "请提取表格中的数据"
                            }
                        }
                    }
                )
                
                document = client.create_document_by_file(
                    dataset_id=DATASET_ID,
                    file_path=sample_file,
                    analysis_strategy=analysis_strategy
                )
                print(f"✓ 上传成功: {document.name} (状态: {document.display_status})")
                
                # 2. 检查文档索引状态
                print("\n2. 检查文档索引状态...")
                max_retries = 10
                for i in range(max_retries):
                    status = client.get_document_status(DATASET_ID, document.id)
                    print(f"  状态检查 {i+1}/{max_retries}: {status['display_status']}")
                    
                    if status['display_status'] == 'available':
                        print("✓ 文档索引完成，可以使用")
                        break
                    elif status['display_status'] == 'error':
                        print("✗ 文档索引失败")
                        break
                    else:
                        time.sleep(2)  # 等待2秒后重试
                
                # 3. 获取文档列表
                print("\n3. 获取文档列表...")
                documents = client.get_documents(DATASET_ID, limit=10)
                print(f"✓ 共有 {documents['total']} 个文档")
                
                for i, doc in enumerate(documents['data'][:3], 1):
                    print(f"  {i}. {doc.name} - 状态: {doc.display_status}")
                
                # 4. 搜索文档
                print("\n4. 搜索文档...")
                search_results = client.get_documents(
                    DATASET_ID, 
                    keyword="人工智能", 
                    limit=5
                )
                print(f"✓ 搜索到 {len(search_results['data'])} 个相关文档")
                
                # 5. 重新索引文档（如果需要）
                # print(f"\n5. 重新索引文档 {document.id}...")
                # reindex_success = client.reindex_document(DATASET_ID, document.id)
                # if reindex_success:
                #     print("✓ 重新索引成功")
                
                # 6. 删除文档（可选）
                # print(f"\n6. 删除文档 {document.id}...")
                # delete_success = client.delete_document(DATASET_ID, document.id)
                # if delete_success:
                #     print("✓ 删除成功")
                
            finally:
                # 清理示例文件
                if os.path.exists(sample_file):
                    os.remove(sample_file)
                    
        except HanHaiClientError as e:
            print(f"✗ 文档管理失败: {e}")


def example_file_upload():
    """文件上传示例"""
    print("\n=== 文件上传示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-app-key"  # 注意：文件上传使用APP_KEY
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 创建示例图片文件（模拟）
            sample_image = "sample_image.txt"  # 实际应该是图片文件
            with open(sample_image, 'w') as f:
                f.write("这是一个模拟的图片文件内容")
            
            try:
                # 上传文件
                print("1. 上传文件到应用平台...")
                uploaded_file = client.upload_file(sample_image, USER_ID)
                
                print(f"✓ 上传成功:")
                print(f"  文件ID: {uploaded_file.id}")
                print(f"  文件名: {uploaded_file.name}")
                print(f"  大小: {uploaded_file.size} bytes")
                print(f"  类型: {uploaded_file.mime_type}")
                print(f"  扩展名: {uploaded_file.extension}")
                
            finally:
                # 清理示例文件
                if os.path.exists(sample_image):
                    os.remove(sample_image)
                    
        except HanHaiClientError as e:
            print(f"✗ 文件上传失败: {e}")


def example_comprehensive_workflow():
    """综合工作流示例"""
    print("\n=== 综合工作流示例 ===")
    
    HOST = "https://your-api-host.com"
    DATASET_KEY = "your-dataset-key"
    APP_KEY = "your-app-key"
    USER_ID = "workflow_user"
    
    try:
        # 1. 使用DATASET_KEY管理知识库
        with create_client(HOST, DATASET_KEY) as dataset_client:
            print("1. 创建知识库...")
            dataset = dataset_client.create_dataset("综合测试知识库")
            print(f"✓ 知识库创建成功: {dataset.id}")
            
            # 创建并上传文档
            doc_file = "workflow_doc.md"
            with open(doc_file, 'w', encoding='utf-8') as f:
                f.write("# 工作流测试文档\n\n这是一个测试文档。")
            
            try:
                document = dataset_client.create_document_by_file(
                    dataset_id=dataset.id,
                    file_path=doc_file
                )
                print(f"✓ 文档上传成功: {document.name}")
            finally:
                os.remove(doc_file)
        
        # 2. 使用APP_KEY进行对话（假设知识库已配置到应用中）
        with create_client(HOST, APP_KEY) as app_client:
            print("\n2. 基于知识库进行对话...")
            # 这里可以进行基于知识库的对话
            # message = app_client.send_message(
            #     query="请介绍一下工作流",
            #     user=USER_ID,
            #     response_mode=ResponseMode.BLOCKING
            # )
            # print(f"AI回答: {message.answer}")
            print("✓ 对话功能准备就绪")
        
        print("\n✓ 综合工作流演示完成")
        
    except HanHaiClientError as e:
        print(f"✗ 综合工作流失败: {e}")


if __name__ == '__main__':
    print("瀚海知识库管理示例")
    print("=" * 50)
    print("注意: 请在代码中配置正确的API地址和密钥后运行")
    print("=" * 50)
    
    # 运行各种示例
    example_dataset_management()
    example_document_management()
    example_file_upload()
    example_comprehensive_workflow()
    
    print("\n示例运行完成！")
    print("\n重要提示:")
    print("1. 知识库管理需要使用 DATASET_KEY")
    print("2. 对话功能需要使用 APP_KEY")
    print("3. 文件上传需要使用 APP_KEY")
    print("4. 请确保API密钥有相应的权限")
