# coding=utf-8
"""
瀚海Agent API客户端测试
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from teamvision.teamvision.ai.hanhai_client import (
    HanHaiClient,
    ResponseMode,
    HanHaiClientError,
    create_client,
    AnalysisStrategy
)


def test_basic_functionality():
    """测试基本功能"""
    # 注意：这里需要替换为实际的API地址和密钥
    host = "https://your-api-host.com"
    api_key = "your-api-key"

    try:
        # 创建客户端
        client = create_client(host, api_key)
        print("✓ 客户端创建成功")
        
        # 测试获取应用配置
        try:
            config = client.get_parameters(user="test_user")
            print("✓ 获取应用配置成功")
        except HanHaiClientError as e:
            print(f"✗ 获取应用配置失败: {e}")
        
        # 测试获取开场白
        try:
            opening = client.get_opening_statement()
            print("✓ 获取开场白成功")
        except HanHaiClientError as e:
            print(f"✗ 获取开场白失败: {e}")
        
        # 测试发送阻塞消息
        try:
            message = client.send_message(
                query="你好",
                user="test_user",
                response_mode=ResponseMode.BLOCKING
            )
            print(f"✓ 发送阻塞消息成功: {message.answer[:50]}...")
            
            # 测试获取会话列表
            conversations = client.get_conversations(user="test_user")
            print(f"✓ 获取会话列表成功，共{len(conversations['data'])}个会话")
            
            # 测试获取消息历史
            messages = client.get_messages(
                conversation_id=message.conversation_id,
                user="test_user"
            )
            print(f"✓ 获取消息历史成功，共{len(messages['data'])}条消息")
            
        except HanHaiClientError as e:
            print(f"✗ 消息相关操作失败: {e}")
        
        # 测试流式消息
        try:
            print("测试流式消息:")
            for event in client.send_message(
                query="请简单介绍一下你自己",
                user="test_user",
                response_mode=ResponseMode.STREAMING
            ):
                if event.event == 'message':
                    answer = event.data.get('answer', '')
                    if answer:
                        print(answer, end='', flush=True)
                elif event.event == 'message_end':
                    print("\n✓ 流式消息测试完成")
                    break
        except HanHaiClientError as e:
            print(f"✗ 流式消息测试失败: {e}")
        
        client.close()
        print("✓ 客户端关闭成功")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")


def test_context_manager():
    """测试上下文管理器"""
    host = "https://your-api-host.com"
    api_key = "your-api-key"
    
    try:
        with create_client(host, api_key) as client:
            print("✓ 上下文管理器测试开始")
            # 在这里可以进行各种操作
            config = client.get_parameters(user="test_user")
            print("✓ 在上下文管理器中成功调用API")
        print("✓ 上下文管理器自动关闭客户端")
    except Exception as e:
        print(f"✗ 上下文管理器测试失败: {e}")


def test_error_handling():
    """测试错误处理"""
    # 使用无效的API地址和密钥进行测试
    client = create_client("https://invalid-host.com", "invalid-key")

    try:
        client.send_message(
            query="测试",
            user="test_user",
            response_mode=ResponseMode.BLOCKING
        )
    except HanHaiClientError as e:
        print(f"✓ 错误处理正常: {e}")
    except Exception as e:
        print(f"✗ 未预期的错误类型: {e}")
    finally:
        client.close()


def test_knowledge_base_management():
    """测试知识库管理功能"""
    print("=== 知识库管理测试 ===")

    # 注意：这里需要替换为实际的API地址和DATASET_KEY
    host = "https://your-api-host.com"
    dataset_key = "your-dataset-key"

    try:
        with create_client(host, dataset_key) as client:
            print("✓ 知识库客户端创建成功")

            # 测试创建知识库
            try:
                dataset = client.create_dataset("测试知识库")
                print(f"✓ 创建知识库成功: {dataset.name} (ID: {dataset.id})")

                # 测试获取知识库列表
                datasets = client.get_datasets(page=1, limit=5)
                print(f"✓ 获取知识库列表成功，共{datasets['total']}个知识库")

                # 测试获取文档列表
                documents = client.get_documents(dataset.id)
                print(f"✓ 获取文档列表成功，共{len(documents['data'])}个文档")

                # 测试删除知识库
                # success = client.delete_dataset(dataset.id)
                # if success:
                #     print("✓ 删除知识库成功")

            except HanHaiClientError as e:
                print(f"✗ 知识库管理操作失败: {e}")

    except Exception as e:
        print(f"✗ 知识库管理测试失败: {e}")


def test_document_management():
    """测试文档管理功能"""
    print("\n=== 文档管理测试 ===")

    host = "https://your-api-host.com"
    dataset_key = "your-dataset-key"
    dataset_id = "your-dataset-id"  # 需要替换为实际的知识库ID

    try:
        with create_client(host, dataset_key) as client:
            print("✓ 文档管理客户端创建成功")

            # 创建测试文档
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write("这是一个测试文档\n\n# 标题\n\n这是测试内容。")
                temp_file = f.name

            try:
                # 测试上传文档
                document = client.create_document_by_file(
                    dataset_id=dataset_id,
                    file_path=temp_file
                )
                print(f"✓ 上传文档成功: {document.name} (状态: {document.display_status})")

                # 测试获取文档状态
                status = client.get_document_status(dataset_id, document.id)
                print(f"✓ 获取文档状态成功: {status['display_status']}")

                # 测试重新索引
                # reindex_success = client.reindex_document(dataset_id, document.id)
                # if reindex_success:
                #     print("✓ 重新索引成功")

                # 测试删除文档
                # delete_success = client.delete_document(dataset_id, document.id)
                # if delete_success:
                #     print("✓ 删除文档成功")

            except HanHaiClientError as e:
                print(f"✗ 文档管理操作失败: {e}")
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.unlink(temp_file)

    except Exception as e:
        print(f"✗ 文档管理测试失败: {e}")


def test_file_upload():
    """测试文件上传功能"""
    print("\n=== 文件上传测试 ===")

    host = "https://your-api-host.com"
    app_key = "your-app-key"  # 注意：文件上传使用APP_KEY

    try:
        with create_client(host, app_key) as client:
            print("✓ 文件上传客户端创建成功")

            # 创建测试文件
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write("这是一个测试上传文件")
                temp_file = f.name

            try:
                # 测试上传文件
                uploaded_file = client.upload_file(temp_file, "test_user")
                print(f"✓ 文件上传成功:")
                print(f"  文件ID: {uploaded_file.id}")
                print(f"  文件名: {uploaded_file.name}")
                print(f"  大小: {uploaded_file.size} bytes")
                print(f"  类型: {uploaded_file.mime_type}")

            except HanHaiClientError as e:
                print(f"✗ 文件上传失败: {e}")
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.unlink(temp_file)

    except Exception as e:
        print(f"✗ 文件上传测试失败: {e}")


if __name__ == '__main__':
    print("=== 瀚海Agent API客户端测试 ===\n")

    print("1. 基本功能测试:")
    test_basic_functionality()

    print("\n2. 上下文管理器测试:")
    test_context_manager()

    print("\n3. 错误处理测试:")
    test_error_handling()

    print("\n4. 知识库管理测试:")
    test_knowledge_base_management()

    print("\n5. 文档管理测试:")
    test_document_management()

    print("\n6. 文件上传测试:")
    test_file_upload()

    print("\n=== 测试完成 ===")
    print("\n注意事项：")
    print("1. 要运行实际测试，请在代码中替换为真实的API地址和密钥")
    print("2. 知识库管理需要使用 DATASET_KEY")
    print("3. 对话功能需要使用 APP_KEY")
    print("4. 文件上传需要使用 APP_KEY")
    print("5. 文档管理测试需要提供有效的知识库ID")
