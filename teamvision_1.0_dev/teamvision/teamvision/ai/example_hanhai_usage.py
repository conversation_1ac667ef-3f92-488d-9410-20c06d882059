# coding=utf-8
"""
瀚海Agent API客户端使用示例
"""

from teamvision.teamvision.ai.hanhai_client import (
    create_client, 
    ResponseMode, 
    HanHaiClientError
)


def example_blocking_chat():
    """阻塞模式对话示例"""
    print("=== 阻塞模式对话示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-api-key"
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 发送第一条消息
            message1 = client.send_message(
                query="你好，请介绍一下你自己",
                user=USER_ID,
                response_mode=ResponseMode.BLOCKING
            )
            
            print(f"用户: 你好，请介绍一下你自己")
            print(f"AI: {message1.answer}")
            print(f"会话ID: {message1.conversation_id}")
            
            # 在同一会话中继续对话
            message2 = client.send_message(
                query="你能帮我做什么？",
                user=USER_ID,
                response_mode=ResponseMode.BLOCKING,
                conversation_id=message1.conversation_id
            )
            
            print(f"\n用户: 你能帮我做什么？")
            print(f"AI: {message2.answer}")
            
            # 获取会话历史
            messages = client.get_messages(
                conversation_id=message1.conversation_id,
                user=USER_ID
            )
            
            print(f"\n会话历史共有 {len(messages['data'])} 条消息")
            
        except HanHaiClientError as e:
            print(f"API错误: {e}")


def example_streaming_chat():
    """流式模式对话示例"""
    print("\n=== 流式模式对话示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-api-key"
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            print("用户: 请写一首关于春天的短诗")
            print("AI: ", end="", flush=True)
            
            # 发送流式消息
            for event in client.send_message(
                query="请写一首关于春天的短诗",
                user=USER_ID,
                response_mode=ResponseMode.STREAMING
            ):
                if event.event == 'message':
                    answer = event.data.get('answer', '')
                    if answer:
                        print(answer, end="", flush=True)
                        
                elif event.event == 'message_end':
                    print("\n[消息结束]")
                    
                    # 获取元数据
                    metadata = event.data.get('metadata', {})
                    if 'usage' in metadata:
                        usage = metadata['usage']
                        print(f"Token使用: {usage['total_tokens']} "
                              f"(输入: {usage['prompt_tokens']}, "
                              f"输出: {usage['completion_tokens']})")
                    break
                    
                elif event.event == 'error':
                    print(f"\n[错误]: {event.data}")
                    break
                    
        except HanHaiClientError as e:
            print(f"API错误: {e}")


def example_conversation_management():
    """会话管理示例"""
    print("\n=== 会话管理示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-api-key"
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 获取会话列表
            conversations = client.get_conversations(user=USER_ID, limit=10)
            print(f"当前用户有 {len(conversations['data'])} 个会话")
            
            # 显示会话信息
            for i, conv in enumerate(conversations['data'][:3], 1):
                print(f"{i}. 会话ID: {conv.id}")
                print(f"   名称: {conv.name}")
                print(f"   创建时间: {conv.created_at}")
                
                # 获取该会话的消息
                messages = client.get_messages(
                    conversation_id=conv.id,
                    user=USER_ID,
                    limit=3
                )
                print(f"   消息数量: {len(messages['data'])}")
                print()
            
            # 如果有会话，可以删除第一个（仅作示例）
            if conversations['data']:
                first_conv = conversations['data'][0]
                print(f"删除会话: {first_conv.name}")
                
                success = client.delete_conversation(
                    conversation_id=first_conv.id,
                    user=USER_ID
                )
                
                if success:
                    print("✓ 会话删除成功")
                else:
                    print("✗ 会话删除失败")
                    
        except HanHaiClientError as e:
            print(f"API错误: {e}")


def example_app_configuration():
    """应用配置示例"""
    print("\n=== 应用配置示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-api-key"
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 获取开场白
            opening = client.get_opening_statement()
            print("开场白:")
            print(f"  内容: {opening.get('opening_statement', '无')}")
            print(f"  建议问题: {opening.get('suggested_questions', [])}")
            
            # 获取应用参数
            params = client.get_parameters(user=USER_ID)
            print(f"\n应用配置:")
            print(f"  语音转文本: {params.get('speech_to_text', {}).get('enabled', False)}")
            print(f"  引用和归属: {params.get('retriever_resource', {}).get('enabled', False)}")
            print(f"  文件上传: {params.get('file_upload', {}).get('image', {}).get('enabled', False)}")
            
        except HanHaiClientError as e:
            print(f"API错误: {e}")


def example_with_inputs():
    """带输入参数的对话示例"""
    print("\n=== 带输入参数的对话示例 ===")
    
    # 配置API信息（请替换为实际值）
    HOST = "https://your-api-host.com"
    API_KEY = "your-api-key"
    USER_ID = "example_user_123"
    
    with create_client(HOST, API_KEY) as client:
        try:
            # 带输入参数的消息
            message = client.send_message(
                query="根据筛选条件查询相关信息",
                user=USER_ID,
                response_mode=ResponseMode.BLOCKING,
                inputs={
                    "filter": "_fs01 == \"现行有效\" and _fi01 == 1718087730",
                    "category": "法律法规",
                    "limit": 10
                }
            )
            
            print(f"用户: 根据筛选条件查询相关信息")
            print(f"输入参数: {message}")
            print(f"AI: {message.answer}")
            
            # 检查是否有检索资源
            if message.retriever_resources:
                print(f"\n检索到 {len(message.retriever_resources)} 个相关资源:")
                for i, resource in enumerate(message.retriever_resources[:3], 1):
                    print(f"{i}. 数据集: {resource.dataset_name}")
                    print(f"   文档: {resource.document_name}")
                    print(f"   内容片段: {resource.content[:100]}...")
                    print()
            
        except HanHaiClientError as e:
            print(f"API错误: {e}")


if __name__ == '__main__':
    print("瀚海Agent API客户端使用示例")
    print("=" * 50)
    print("注意: 请在代码中配置正确的API地址和密钥后运行")
    print("=" * 50)
    
    # 运行各种示例
    example_blocking_chat()
    example_streaming_chat()
    example_conversation_management()
    example_app_configuration()
    example_with_inputs()
    
    print("\n示例运行完成！")
