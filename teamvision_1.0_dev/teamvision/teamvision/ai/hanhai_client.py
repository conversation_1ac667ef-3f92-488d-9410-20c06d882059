# coding=utf-8
"""
瀚海Agent API客户端
支持对话消息发送、会话管理、消息历史查询等功能
"""

import json
import requests
from typing import Dict, List, Optional, Union, Iterator, Any
from dataclasses import dataclass
from enum import Enum
from django.conf import settings


class ResponseMode(Enum):
    """响应模式枚举"""
    STREAMING = "streaming"
    BLOCKING = "blocking"


@dataclass
class Usage:
    """模型用量信息"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


@dataclass
class RetrieverResource:
    """检索资源信息"""
    dataset_id: str
    dataset_name: str
    document_id: str
    document_name: str
    content: str


@dataclass
class ChatMessage:
    """聊天消息"""
    id: str
    conversation_id: str
    query: str
    answer: str
    reason: Optional[str] = None
    created_at: int = 0
    retriever_resources: Optional[List[RetrieverResource]] = None


@dataclass
class Conversation:
    """会话信息"""
    id: str
    name: str
    created_at: int


@dataclass
class StreamEvent:
    """流式事件"""
    event: str
    id: str
    conversation_id: str
    created_at: int
    data: Dict[str, Any]


@dataclass
class Dataset:
    """知识库信息"""
    id: str
    name: str
    document_count: int = 0
    created_at: int = 0


@dataclass
class Document:
    """文档信息"""
    id: str
    name: str
    display_status: str
    created_at: int = 0
    error: Optional[str] = None


@dataclass
class UploadedFile:
    """上传文件信息"""
    id: str
    name: str
    size: int
    extension: str
    mime_type: str
    created_at: int


@dataclass
class AnalysisStrategy:
    """文档解析策略配置"""
    image_understanding: Optional[Dict[str, Any]] = None
    table_understanding: Optional[Dict[str, Any]] = None


class HanHaiClientError(Exception):
    """瀚海客户端异常"""
    def __init__(self, status_code: int, code: str, message: str):
        self.status_code = status_code
        self.code = code
        self.message = message
        super().__init__(f"[{status_code}] {code}: {message}")


class HanHaiClient:
    """瀚海Agent API客户端"""

    def __init__(self, base_url: str, api_key: str, timeout: int = 60):
        """
        初始化客户端

        Args:
            host: API服务地址
            api_key: API密钥
            timeout: 请求超时时间（秒）
        """
        self.base_url = getattr(settings, 'HANHAI_API_URL', 'http://localhost:9380')
        self.api_key = getattr(settings, 'HANHAI_API_KEY', '')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        kwargs.setdefault('timeout', self.timeout)

        try:
            response = self.session.request(method, url, **kwargs)

            # 检查错误响应
            if not response.ok:
                try:
                    error_data = response.json()
                    raise HanHaiClientError(
                        status_code=response.status_code,
                        code=error_data.get('code', 'unknown_error'),
                        message=error_data.get('message', 'Unknown error')
                    )
                except json.JSONDecodeError:
                    raise HanHaiClientError(
                        status_code=response.status_code,
                        code='http_error',
                        message=response.text or f'HTTP {response.status_code}'
                    )

            return response
        except requests.RequestException as e:
            raise HanHaiClientError(0, 'request_error', str(e))


    # ==================== agent 对话功能 ====================

    def send_message_to_agent(self, query: str, user: str, response_mode: ResponseMode = ResponseMode.BLOCKING, conversation_id: Optional[str] = None, 
                     inputs: Optional[Dict[str, Any]] = None) -> Union[ChatMessage, Iterator[StreamEvent]]:
        """
        发送对话消息

        Args:
            query: 用户输入/提问内容
            user: 用户标识
            response_mode: 响应模式（streaming/blocking）
            conversation_id: 会话ID，不填写开启新的对话
            inputs: 输入参数

        Returns:
            blocking模式返回ChatMessage，streaming模式返回StreamEvent迭代器
        """
        data = {
            'query': query,
            'response_mode': response_mode.value,
            'user': user
        }

        if conversation_id:
            data['conversation_id'] = conversation_id

        if inputs:
            data['inputs'] = inputs

        if response_mode == ResponseMode.STREAMING:
            return self._send_streaming_message_to_agent(data)
        else:
            return self._send_blocking_message_to_agent(data)

    def _send_blocking_message_to_agent(self, data: Dict[str, Any]) -> ChatMessage:
        """发送阻塞模式消息"""
        response = self._make_request('POST', '/v1/chat-messages', json=data)
        result = response.json()

        # 解析检索资源
        retriever_resources = None
        if 'metadata' in result and 'retriever_resources' in result['metadata']:
            retriever_resources = [
                RetrieverResource(**resource)
                for resource in result['metadata']['retriever_resources']
            ]

        return ChatMessage(
            id=result['id'],
            conversation_id=result['conversation_id'],
            query=data['query'],
            answer=result['answer'],
            reason=result.get('reason'),
            created_at=result['created_at'],
            retriever_resources=retriever_resources
        )

    def _send_streaming_message_to_agent(self, data: Dict[str, Any]) -> Iterator[StreamEvent]:
        """发送流式消息"""
        response = self._make_request('POST', '/v1/chat-messages', json=data, stream=True)

        for line in response.iter_lines(decode_unicode=True):
            if line and line.startswith('data: '):
                try:
                    event_data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                    yield StreamEvent(
                        event=event_data['event'],
                        id=event_data['id'],
                        conversation_id=event_data['conversation_id'],
                        created_at=event_data['created_at'],
                        data=event_data
                    )
                except json.JSONDecodeError:
                    continue

    def get_conversations_agent(self, user: str, last_id: Optional[str] = None, limit: int = 20) -> Dict[str, Any]:
        """
        获取会话列表

        Args:
            user: 用户标识
            last_id: 当前页最后面一条记录的ID
            limit: 返回条数（1-100）

        Returns:
            包含会话列表的字典
        """
        params = {'user': user, 'limit': limit}
        if last_id:
            params['last_id'] = last_id

        response = self._make_request('GET', '/v1/conversations', params=params)
        result = response.json()

        # 转换会话数据
        conversations = [
            Conversation(
                id=conv['id'],
                name=conv['name'],
                created_at=conv['created_at']
            )
            for conv in result['data']
        ]

        return {
            'data': conversations,
            'has_more': result['has_more'],
            'limit': result['limit']
        }

    def get_messages_agent(self, conversation_id: str, user: str, first_id: Optional[str] = None, limit: int = 20) -> Dict[str, Any]:
        """
        获取会话历史消息

        Args:
            conversation_id: 会话ID
            user: 用户标识
            first_id: 当前页第一条聊天记录的ID
            limit: 返回条数（1-100）

        Returns:
            包含消息列表的字典
        """
        params = {
            'conversation_id': conversation_id,
            'user': user,
            'limit': limit
        }
        if first_id:
            params['first_id'] = first_id

        response = self._make_request('GET', '/v1/messages', params=params)
        result = response.json()

        # 转换消息数据
        messages = []
        for msg in result['data']:
            retriever_resources = None
            if 'retriever_resources' in msg:
                retriever_resources = [
                    RetrieverResource(**resource)
                    for resource in msg['retriever_resources']
                ]

            messages.append(ChatMessage(
                id=msg['id'],
                conversation_id=msg['conversation_id'],
                query=msg['query'],
                answer=msg['answer'],
                reason=msg.get('reason'),
                created_at=msg['created_at'],
                retriever_resources=retriever_resources
            ))

        return {
            'data': messages,
            'has_more': result['has_more'],
            'limit': result['limit']
        }

    def delete_conversation_agent(self, conversation_id: str, user: str) -> bool:
        """
        删除会话

        Args:
            conversation_id: 会话ID
            user: 用户标识

        Returns:
            删除是否成功
        """
        data = {'user': user}
        response = self._make_request('DELETE', f'/conversations/{conversation_id}', json=data)
        result = response.json()
        return result.get('result') == 'success'

    def get_parameters_agent(self, user: str) -> Dict[str, Any]:
        """
        获取应用配置信息

        Args:
            user: 用户标识

        Returns:
            应用配置信息
        """
        params = {'user': user}
        response = self._make_request('GET', '/parameters', params=params)
        return response.json()

    def get_suggested_questions_agent(self, message_id: str, user: str) -> List[str]:
        """
        获取消息建议问题

        Args:
            message_id: 消息ID
            user: 用户标识

        Returns:
            建议问题列表
        """
        params = {'user': user}
        response = self._make_request('GET', f'/v1/messages/{message_id}/suggested', params=params)
        result = response.json()
        return result.get('data', [])

    def get_opening_statement_agent(self) -> Dict[str, Any]:
        """
        获取应用开场白

        Returns:
            包含开场白和建议问题的字典
        """
        response = self._make_request('GET', '/v1/apps/opening-statement')
        result = response.json()
        return result.get('data', {})

    # ==================== workflow 对话功能 ====================


    # ==================== 知识库管理方法 ====================

    def create_dataset(self, name: str) -> Dataset:
        """
        创建空知识库

        Args:
            name: 知识库名称（1-40个字符）

        Returns:
            Dataset: 创建的知识库信息
        """
        data = {'name': name}
        response = self._make_request('POST', '/v1/datasets', json=data)
        result = response.json()

        return Dataset(
            id=result['id'],
            name=name,
            document_count=0,
            created_at=0  # 创建时返回的数据中没有时间戳
        )

    def get_datasets(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取知识库列表

        Args:
            page: 页码，默认第1页
            limit: 返回条数，默认20，范围1-100

        Returns:
            包含知识库列表的字典
        """
        params = {'page': page, 'limit': limit}
        response = self._make_request('GET', '/v1/datasets', params=params)
        result = response.json()

        # 转换知识库数据
        datasets = [
            Dataset(
                id=dataset['id'],
                name=dataset['name'],
                document_count=dataset['document_count'],
                created_at=dataset['created_at']
            )
            for dataset in result['data']
        ]

        return {
            'data': datasets,
            'has_more': result['has_more'],
            'limit': result['limit'],
            'total': result['total'],
            'page': result['page']
        }

    def delete_dataset(self, dataset_id: str) -> bool:
        """
        删除知识库

        Args:
            dataset_id: 知识库ID

        Returns:
            删除是否成功
        """
        response = self._make_request('DELETE', f'/v1/datasets/{dataset_id}')
        return response.status_code == 204

    # ==================== 文档管理方法 ====================

    def create_document_by_file(self, dataset_id: str, file_path: str, analysis_strategy: Optional[AnalysisStrategy] = None) -> Document:
        """
        通过文件创建文档

        Args:
            dataset_id: 知识库ID
            file_path: 文件路径
            analysis_strategy: 文档解析策略配置（可选）

        Returns:
            Document: 创建的文档信息
        """
        files = {'file': open(file_path, 'rb')}
        data = {}

        if analysis_strategy:
            data['data'] = json.dumps({
                'analysis_strategy': {
                    'image_understanding': analysis_strategy.image_understanding,
                    'table_understanding': analysis_strategy.table_understanding
                }
            })

        try:
            response = self._make_request(
                'POST',
                f'/v1/datasets/{dataset_id}/document/create-by-file',
                files=files,
                data=data
            )
            result = response.json()

            document_data = result['document']
            return Document(
                id=document_data['id'],
                name=document_data['name'],
                display_status=document_data['display_status'],
                created_at=document_data.get('created_at', 0)
            )
        finally:
            files['file'].close()

    def get_documents(self, dataset_id: str, keyword: Optional[str] = None, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取知识库文档列表

        Args:
            dataset_id: 知识库ID
            keyword: 搜索关键词（可选）
            page: 页码，默认第1页
            limit: 返回条数，默认20，范围1-100

        Returns:
            包含文档列表的字典
        """
        params = {'page': page, 'limit': limit}
        if keyword:
            params['keyword'] = keyword

        response = self._make_request('GET', f'/v1/datasets/{dataset_id}/documents', params=params)
        result = response.json()

        # 转换文档数据
        documents = [
            Document(
                id=doc['id'],
                name=doc['name'],
                display_status=doc['display_status'],
                created_at=doc['created_at'],
                error=doc.get('error')
            )
            for doc in result['data']
        ]

        return {
            'data': documents,
            'has_more': result['has_more'],
            'limit': result['limit'],
            'total': result['total'],
            'page': result['page']
        }

    def delete_document(self, dataset_id: str, document_id: str) -> bool:
        """
        删除文档

        Args:
            dataset_id: 知识库ID
            document_id: 文档ID

        Returns:
            删除是否成功
        """
        response = self._make_request(
            'DELETE',
            f'/v1/datasets/{dataset_id}/documents/{document_id}'
        )
        return response.status_code == 204

    def get_document_status(self, dataset_id: str, document_id: str) -> Dict[str, str]:
        """
        获取文档的索引状态

        Args:
            dataset_id: 知识库ID
            document_id: 文档ID

        Returns:
            包含文档ID和状态的字典
        """
        response = self._make_request(
            'GET',
            f'/v1/datasets/{dataset_id}/documents/{document_id}/display-status'
        )
        return response.json()

    def reindex_document(self, dataset_id: str, document_id: str) -> bool:
        """
        重新索引文档

        Args:
            dataset_id: 知识库ID
            document_id: 文档ID

        Returns:
            重新索引是否成功
        """
        response = self._make_request('POST', f'/v1/datasets/{dataset_id}/documents/{document_id}/reindex')
        return response.status_code == 204

    # ==================== 文件上传方法 ====================

    def upload_file(self, file_path: str, user: str) -> UploadedFile:
        """
        上传文件至应用平台

        Args:
            file_path: 文件路径
            user: 用户标识

        Returns:
            UploadedFile: 上传的文件信息
        """
        files = {'file': open(file_path, 'rb')}
        data = {'user': user}

        try:
            response = self._make_request('POST', '/v1/files/upload', files=files, data=data)
            result = response.json()

            return UploadedFile(
                id=result['id'],
                name=result['name'],
                size=result['size'],
                extension=result['extension'],
                mime_type=result['mime_type'],
                created_at=result['created_at']
            )
        finally:
            files['file'].close()

    def close(self):
        """关闭客户端会话"""
        if self.session:
            self.session.close()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """上下文管理器出口"""
        self.close()
        return False

# 使用示例
if __name__ == '__main__':
    # 基本使用示例
    client = HanHaiClient('https://your-api-host.com', 'your-api-key')

    try:
        # ========== 对话功能示例 ==========
        # 发送阻塞消息
        message = client.send_message(query="你好", user="user123", response_mode=ResponseMode.BLOCKING)
        print(f"回答: {message.answer}")

        # 发送流式消息
        for event in client.send_message(
            query="请介绍一下你自己",
            user="user123",
            response_mode=ResponseMode.STREAMING,
            conversation_id=message.conversation_id
        ):
            if event.event == 'message':
                print(event.data.get('answer', ''), end='', flush=True)
            elif event.event == 'message_end':
                print("\n消息结束")
                break

        # 获取会话列表
        conversations = client.get_conversations_agent(user="user123")
        print(f"会话数量: {len(conversations['data'])}")

        # ========== 知识库管理示例 ==========
        # 创建知识库
        dataset = client.create_dataset("测试知识库")
        print(f"创建知识库: {dataset.name} (ID: {dataset.id})")

        # 获取知识库列表
        datasets = client.get_datasets(page=1, limit=10)
        print(f"知识库总数: {datasets['total']}")

        # 上传文件到知识库
        # document = client.create_document_by_file(
        #     dataset_id=dataset.id,
        #     file_path="/path/to/your/document.pdf"
        # )
        # print(f"上传文档: {document.name} (状态: {document.display_status})")

        # 获取文档列表
        documents = client.get_documents(dataset.id)
        print(f"文档数量: {len(documents['data'])}")

        # 上传文件到应用平台
        # uploaded_file = client.upload_file("/path/to/image.jpg", "user123")
        # print(f"上传文件: {uploaded_file.name} (大小: {uploaded_file.size} bytes)")

    except HanHaiClientError as e:
        print(f"API错误: {e}")
    finally:
        client.close()