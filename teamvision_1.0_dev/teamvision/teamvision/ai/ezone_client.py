import base64
from typing import List
import requests
import warnings
import json

class EzoneClient:
    def __init__(self, base_url, token):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'access_token': token,
            'Content-Type': 'application/json'
        }

    def get_page_children(self, page_id: str, space_id: str):
        """获取页面的子页面列表"""
        url = f"{self.base_url}/page/{page_id}/children?spaceId={space_id}"
        try: 
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0 and "data" in data:
                children = data["data"]
                warnings.warn(f"成功获取子页面列表，页面 ID: {page_id}，子页面数量: {len(children)}")
                return children
            else:
                warnings.warn(f"获取子页面列表失败，页面 ID: {page_id}，错误码: {data.get('code')}，错误信息: {data.get('message')}")
                return []
        except requests.RequestException as e:
            Exception(f"获取子页面列表失败: {str(e)}")
        except json.JSONDecodeError as e:
            Exception(f"解析子页面响应失败: {str(e)}")
        return []

    def get_page_detail(self, page_id: str):
        """获取页面详细内容"""
        url = f"{self.base_url}/page/{page_id}/detail?draftFirst=false&desensitization=true"
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0 and "data" in data:
                return data["data"]

        except requests.RequestException as e:
            Exception(f"获取页面详情失败，页面 ID: {page_id}，错误码: {data.get('code')}，错误信息: {data.get('message')}")
        except json.JSONDecodeError as e:
           Exception(f"解析页面详情响应失败: {str(e)}")
        return None
    

class EzoneCodeClient:
    """Ezone代码库客户端"""
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "access_token": f"{self.token}",
            "Content-Type": "application/json"
        }

    def get_parent_ids(self, repo_id: str ,commit_id: str) -> List[str]:
        """获取commit的parentIds
        
        request: https://ezone.ksyun.com/v1/code/v2/repos/1017837821934460928/commits/48c2bc995017e7ae71a7ea876f9373d3cf099b16
          respone:
            {
            "code": 0,
            "data": {
                "id": "48c2bc995017e7ae71a7ea876f9373d3cf099b16",
                "parentIds": [
                    "1e076febf8e6b8033aff4ee99ad6bc368de92003",
                    "be4bf13662dced5b8f1d6a3fa7e4cc14684508cb"
                ],
                "author": {
                    "name": "puwenbin",
                    "email": "<EMAIL>",
                    "createTime": 1748421651000
                },
                "committer": {
                    "name": "puwenbin",
                    "email": "<EMAIL>",
                    "createTime": 1748421651000
                },
                "message": "Merge V1.0-T-baichenggang into V1.0-T\n",
                "wrappedMessage": "",
                "createTime": 1748421651000,
                "tags": [],
                "branches": []
            }
          }
        """
        
        url = f"{self.base_url}/v1/code/v2/repos/{repo_id}/commits/{commit_id}"
        response = requests.get(url, headers=self.headers)
        try:
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0 and "data" in data:
                return data["data"]["parentIds"]
        except requests.RequestException as e:
            Exception(f"获取commit的parentIds失败: {str(e)}")
        except json.JSONDecodeError as e:
            Exception(f"解析commit的parentIds响应失败: {str(e)}")
        return []


    def get_commit_diffs(self, repo_id: str, src_blob_id: str, dest_blob_id: str) -> str:
        """获取两次 commit 包含的所有文件
        request: https://ezone.ksyun.com/v1/code/v2/repos/1017837821934460928/diffs/stat?srcRef=48c2bc995017e7ae71a7ea876f9373d3cf099b16&destRef=1e076febf8e6b8033aff4ee99ad6bc368de92003&srcRefType=&destRefType=&nameOnly=false
        respone:
        {
            "code": 0,
            "data": {
                "total": "35",
                "insertion": "24",
                "deletion": "11",
                "fileDiffs": [
                    {
                        "total": 6,
                        "insertion": 3,
                        "deletion": 3,
                        "path": "modules/rewrite/force/rewrite_url.lua",
                        "srcBlobId": "6be0254fd6812b6624c709183c4bbc49e505497a",
                        "destBlobId": "7ca019d7065ecb66581b7ca5981ee513ffba4ab4",
                        "changeType": "MODIFY"
                    },
                    {
                        "total": 8,
                        "insertion": 0,
                        "deletion": 8,
                        "path": "modules/rewrite/ks_rewrite_first_force_module.lua",
                        "srcBlobId": "dd835b779eab57c1a76aae86926e3e6e9362a265",
                        "destBlobId": "3fa946579f400881e56ae3bc026ad1845d205da4",
                        "changeType": "MODIFY"
                    },
                    {
                        "total": 21,
                        "insertion": 21,
                        "deletion": 0,
                        "path": "modules/rewrite/ks_rewrite_force_module.lua",
                        "srcBlobId": "9869dedbeaec80e7100ee840cd9b8940bd6b56ec",
                        "destBlobId": "bbb1cc9a809fe9048a696e5de32b23ae38ec7a36",
                        "changeType": "MODIFY"
                    }
                ],
                "hasAnyMore": false
            }
        }
        """
        url = f"{self.base_url}/v1/code/v2/repos/{repo_id}/diffs/stat?srcRef={src_blob_id}&destRef={dest_blob_id}&srcRefType=&destRefType=&nameOnly=false"
        try:
          response = requests.get(url, headers=self.headers)
          response.raise_for_status()
          data = response.json()
          if data.get("code") == 0 and "data" in data:
              return data["data"]["fileDiffs"]
        except requests.RequestException as e:
            Exception(f"获取文件的diff失败: {str(e)}")
        except json.JSONDecodeError as e:
            Exception(f"解析文件的diff响应失败: {str(e)}")
        return []



    def get_file_diff(self, repo_id: str, blob_id: str, path: str) -> str:
        """获取单个文件的diff
        request: https://ezone.ksyun.com/v1/code/v2/repos/1017837821934460928/files/view/?blobId=6be0254&fileType=TEXT&path=modules%2Frewrite%2Fforce%2Frewrite_url.lua
        respone:
        {
        "code": 0,
        "data": {
            "path": "modules/rewrite/force/rewrite_url.lua",
            "blobId": "6be0254fd6812b6624c709183c4bbc49e505497a",
            "contentType": "text/plain",
            "length": "12732",
            "binary": false,
            "lfs": false,
            "large": false,
            "bytes": "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",
            "desensitized": true
        }
        }
        """
        url = f"{self.base_url}/v1/code/v2/repos/{repo_id}/files/view/?blobId={blob_id}&fileType=TEXT&path={path}"
        response = requests.get(url, headers=self.headers)
        try:
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0 and "data" in data:
                diff_content = data["data"]["bytes"]
                return base64.b64decode(diff_content).decode("utf-8")
        except requests.RequestException as e:
            Exception(f"获取文件内容失败: {str(e)}")
        except json.JSONDecodeError as e:
            Exception(f"解析文件内容响应")


    def get_commit_diff(self, repo_id: str, commit_id: str) -> List:
        """
        step1: get get_parent_ids
        setp2: get get_commit_diffs
        setp3: get get_file_diff
        """
        # 增加一些异常处理
        # 1. 检查parent_ids是否为空 
        parent_ids = self.get_parent_ids(repo_id, commit_id)
        if not parent_ids:
            return ""
        file_diffs = self.get_commit_diffs(repo_id, parent_ids[0], commit_id)
        if not file_diffs:
            return ""
        commit_diff_content = []
        for file_diff in file_diffs:
            file_diff_content =  self.get_file_diff(repo_id, file_diff["srcBlobId"], file_diff["destBlobId"], file_diff["path"])
            commit_diff_content.append(file_diff_content)
        return commit_diff_content
    
