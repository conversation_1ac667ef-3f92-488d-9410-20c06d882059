# coding=utf-8
import re, json
from typing import Dict, List, Any
from gatesidelib.common.simplelogger import SimpleLogger
from teamvision.ai.data_class import TestCaseGenerationRequest, TestCaseGenerationResponse, TestCaseItem
from teamvision.ai.models import AIGenerationSession
from teamvision.ai.ragflow_client_api import RAGFlowClient
hanhai_client = HanHaiClient('https://your-api-host.com', 'your-api-key')

class AITestAgent:
    """AI测试 Agent - 提供AI测试相关的提示词和业务逻辑处理"""

    @staticmethod
    def build_system_prompt() -> str:
        """构建系统提示词"""
        return """
你是一个专业的软件测试工程师，你对软件测试方法论和测试工具有深入的了解，专门负责根据需求描述生成高质量的测试用例。

## 你的职责
1. 分析用户提供的需求描述
2. 基于需求生成全面、准确的测试用例
3. 确保测试用例覆盖主要功能点、边界条件和异常场景
4. 提供结构化的测试用例输出

## 测试用例生成规则
1. 每个测试用例必须包含：标题、描述、前置条件(测试步骤)、预期结果
2. 预期结果要明确、可验证，测试步骤要具体、可执行、易理解
3. 根据重要性设置优先级（1-高，2-中，3-低）
4. 为测试用例添加合适的标签
5. 当知识库中的所有内容都与问题无关时，不得编造内容。

## 输出格式
请严格按照以下JSON格式输出：

```json
{{
  "title": "测试用例标题",
  "description": "测试用例描述",
  "precondition": "前置条件, 测试步骤",
  "expectResult": "预期结果",
  "priority": 1,
  "is_group": true,
  "children": [{{
      "title": "测试用例标题",
      "description": "测试用例描述",
      "precondition": "前置条件, 测试步骤",
      "expectResult": "预期结果",
      "priority": 1,
      "is_group": false,
  }}]
}}
```

## 知识库内容
{knowledge}

请基于以上知识库内容和用户需求，生成相应的测试用例。
        """.strip()

    @staticmethod
    def build_optimize_test_case_prompt(test_case_data: Dict, optimization_type: str = 'quality') -> str:
        """构建测试用例优化提示词"""
        prompt = f"""
请优化以下测试用例，优化类型：{optimization_type}

原测试用例：
{json.dumps(test_case_data, ensure_ascii=False, indent=2)}

请从以下方面进行优化：
1. 测试步骤的完整性和可执行性
2. 预期结果的明确性
3. 边界条件的覆盖
4. 异常场景的考虑

请返回优化后的测试用例，格式与原格式相同。
        """
        return prompt

    @staticmethod
    def build_analyze_test_coverage_prompt(requirement_text: str, test_cases_text: str) -> str:
        """构建测试覆盖度分析提示词"""
        prompt = f"""
请分析以下测试用例对需求的覆盖情况：

需求描述：
{requirement_text}

测试用例：
{test_cases_text}

请从以下维度分析：
1. 功能覆盖度（百分比）
2. 边界条件覆盖
3. 异常场景覆盖
4. 缺失的测试场景
5. 改进建议

请以JSON格式返回分析结果：
```json
{{
  "coverage_percentage": 85,
  "functional_coverage": "良好",
  "boundary_coverage": "需要改进",
  "exception_coverage": "一般",
  "missing_scenarios": ["场景1", "场景2"],
  "suggestions": ["建议1", "建议2"]
}}
```
        """
        return prompt

    @staticmethod
    def build_user_query(request: TestCaseGenerationRequest) -> str:
        """构建用户查询"""
        query_template = """
你是一个专业的软件测试工程师，你对软件测试方法论和测试工具有深入的了解，专门负责根据需求描述生成高质量的测试用例。

## 需求信息
**需求名称**: {requirement_title}
**需求描述**: {requirement_description}
**测试类型**: {generation_type}
**生成数量**: {case_count}个

## 生成要求
请根据以上需求信息，生成对应的测试用例。确保：
1. 覆盖主要功能场景
2. 包含边界条件测试
3. 考虑异常情况处理
4. 测试步骤具体可执行
5. 预期结果明确可验证

请严格按照以下JSON格式输出测试用例:

```json
{{
  "title": "测试用例标题",
  "description": "测试用例描述",
  "precondition": "前置条件, 测试步骤",
  "expect_result": "预期结果",
  "priority": 1,
  "is_group": true,
  "children": [{{
      "title": "测试用例标题",
      "description": "测试用例描述",
      "precondition": "前置条件, 测试步骤",
      "expect_result": "预期结果",
      "priority": 1,
      "is_group": false,
  }}]
}}
```
""".format(
            requirement_title=request.requirement_description['title'],
            requirement_description=request.requirement_description['description'],
            generation_type=request.generation_type,
            case_count=request.case_count
        )

        return query_template.strip()

    @staticmethod
    def generate_smart_response(user_message: str, session: AIGenerationSession) -> str:
        """生成智能响应"""

        # 去除首尾空格并转换为小写进行匹配
        trimmed_message = user_message.strip()

        # 精确匹配特定消息
        if trimmed_message == '请帮我生成测试用例':
            return "<h4>请选择需求</h4><p>我将会基于您的需求，为您生成相关的测试用例。</p>"

        # 其他消息的智能匹配
        lower_message = trimmed_message.lower()

        if '生成' in lower_message and '测试用例' in lower_message:
            return "我可以帮您生成测试用例。请先选择具体的需求，然后我会为您生成相应的测试用例。"
        elif '分析' in lower_message and '用例' in lower_message:
            return "我可以帮您分析测试用例的完整性和质量。请提供具体的测试用例信息。"
        elif '补全' in lower_message or '完善' in lower_message:
            return "我可以帮您补全和完善测试用例。请提供需要补全的测试用例详情。"
        elif '覆盖' in lower_message or '覆盖度' in lower_message:
            return "我可以帮您分析测试覆盖度。请提供需求描述，我会分析当前测试用例的覆盖情况。"
        else:
            return f"收到您的消息：{user_message}。我是AI测试助手，可以帮您生成测试用例、分析用例质量、补全用例内容等。请告诉我您需要什么帮助？"
        
    @staticmethod
    def generate_test_cases_with_ragflow(request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """使用RAGFlow生成测试用例"""
        try:
            # 构建用户查询
            user_query = AITestAgent.build_user_query(request)

            # 发送请求并获取响应
            SimpleLogger.info(f"发送测试用例生成请求: {user_query}")
            ragflow_agent_client = RAGFlowClient(mode="agent")
            response_content = ragflow_agent_client.ask(user_query, stream=False)
            SimpleLogger.info(f"RAGFlow响应内容: {response_content}")
            # 解析响应
            return AITestAgent.parse_test_case_response(response_content, request)

        except Exception as e:
            SimpleLogger.exception(f"生成测试用例失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"生成失败: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )

    @staticmethod
    def optimize_test_case_with_ragflow(ragflow_client, test_case_data: Dict, optimization_type: str = 'quality') -> Dict:
        """使用RAGFlow优化测试用例"""
        try:
            prompt = AITestAgent.build_optimize_test_case_prompt(test_case_data, optimization_type)
            response_content = ragflow_client.ask(prompt, stream=False)

            if response_content and not response_content.startswith("对话失败"):
                optimized_data = AITestAgent.extract_json_from_response(response_content)
                return optimized_data
            else:
                return test_case_data

        except Exception as e:
            SimpleLogger.exception(f"测试用例优化失败: {str(e)}")
            return test_case_data  # 返回原数据

    @staticmethod
    def analyze_test_coverage_with_ragflow(ragflow_client, requirement_text: str, test_cases_data: List[Dict]) -> Dict:
        """使用RAGFlow分析测试覆盖度"""
        try:
            test_cases_text = json.dumps(test_cases_data, ensure_ascii=False, indent=2)
            prompt = AITestAgent.build_analyze_test_coverage_prompt(requirement_text, test_cases_text)

            response_content = ragflow_client.ask(prompt, stream=False)

            if response_content:
                analysis_data = AITestAgent.extract_json_from_response(response_content)
                return analysis_data
            else:
                return {
                    'coverage_percentage': 0,
                    'missing_scenarios': [],
                    'suggestions': []
                }

        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析失败: {str(e)}")
            return {
                'error': str(e),
                'coverage_percentage': 0,
                'missing_scenarios': [],
                'suggestions': []
            }

    @staticmethod
    def parse_test_case_response(response_content: str, request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """解析RAGFlow响应"""
        try:
            if not response_content:
                raise ValueError("RAGFlow返回空响应")

            # 尝试从响应中提取JSON
            test_cases_data = AITestAgent.extract_json_from_response(response_content)
            SimpleLogger.info(f"提取的JSON数据: {test_cases_data}")

            # 确保test_cases_data是一个列表
            if type(test_cases_data) is list:
                test_cases_list = test_cases_data
            else:
                # 如果是单个对象，包装成列表
                test_cases_list = [test_cases_data]

            # 处理每个测试用例，确保是字典格式
            processed_cases = []
            for test_case in test_cases_list:
                if type(test_case) is str:
                    test_case = json.loads(test_case)
                processed_cases.append(test_case)

            return TestCaseGenerationResponse(
                success=True,
                message="测试用例生成成功",
                test_cases=processed_cases,
                generation_id="",  # 字符串响应没有ID
                metadata={
                    'generation_time': '',
                    'session_id': '',
                    'request_params': {
                        'generation_type': request.generation_type
                    }
                }
            )

        except Exception as e:
            SimpleLogger.exception(f"解析RAGFlow响应失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"响应解析失败: {str(e)}",
                test_cases={},
                generation_id="",
                metadata={}
            )

    @staticmethod
    def extract_json_from_response(response_text: str) -> Dict:
        """从响应文本中提取JSON数据"""
        text = response_text.strip()

        # 尝试直接解析JSON
        try:
            clean_match = text.replace('\\"', '"')
            return json.loads(clean_match)
        except json.JSONDecodeError:
            pass

        # 尝试从markdown代码块中提取JSON
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, text, re.DOTALL)

        try:
            clean_match = text.replace('\\"', '"')
            return json.loads(clean_match)
        except json.JSONDecodeError:
            pass

        # 尝试从markdown代码块中提取JSON list
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, text, re.DOTALL)

        for match in matches:
            try:
                clean_match = match.replace('\\"', '"')
                return json.loads(clean_match)
            except json.JSONDecodeError:
                continue

        # 尝试从普通代码块中提取JSON
        code_pattern = r'```\s*(.*?)\s*```'
        matches = re.findall(code_pattern, text, re.DOTALL)

        for match in matches:
            try:
                clean_match = match.replace('\\"', '"')
                return json.loads(clean_match)
            except json.JSONDecodeError:
                continue

        # 尝试查找JSON对象
        json_obj_pattern = r'\{.*\}'
        matches = re.findall(json_obj_pattern, text, re.DOTALL)

        for match in matches:
            try:
                clean_match = match.replace('\\"', '"')
                return json.loads(clean_match)
            except json.JSONDecodeError:
                continue

        raise ValueError("无法从响应中提取有效的JSON数据")

    @staticmethod
    def requirement_analysis(requirement: str) -> Dict:
        """调用llm对需求进行分析"""
        try:
            prompt = AITestAgent.build_requirement_analysis_prompt(requirement)
            ragflow_client = RAGFlowClient(mode="agent")
            response_content = ragflow_client.ask(prompt, stream=False)
            analysis_data = AITestAgent.extract_json_from_response(response_content)
            return analysis_data
        except Exception as e:
            SimpleLogger.exception(f"需求分析失败: {str(e)}")
            return {
                'error': str(e),
                'analysis': {}
            }
